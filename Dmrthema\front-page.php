<?php get_header(); ?>

<!-- Slider main container -->
<div class="main-slider swiper">
    <?php
    $slider_query = new WP_Query(array(
        'post_type' => 'slider',
        'posts_per_page' => -1, // Tüm slider öğ<PERSON>rini göster
    ));

    if ($slider_query->have_posts()) :
        // Additional required wrapper
        echo '<div class="swiper-wrapper">';
        while ($slider_query->have_posts()) : $slider_query->the_post();
            // Slides
            echo '<div class="swiper-slide">';
            // Eğer öne çıkan görsel varsa (resim)
            if (has_post_thumbnail()) {
                the_post_thumbnail('full'); // Resmi tam boyutta göster
            }
            // Eğer içerik varsa (video embed kodu vb.)
            if (get_the_content()) {
                echo '<div class="video-container">';
                the_content();
                echo '</div>';
            }
            echo '</div>';
        endwhile;
        echo '</div>';

        // If we need pagination
        echo '<div class="swiper-pagination"></div>';

        // If we need navigation buttons
        echo '<div class="swiper-button-prev"></div>';
        echo '<div class="swiper-button-next"></div>';

        wp_reset_postdata();
    else :
        // Slider öğesi bulunamazsa gösterilecek mesaj
        echo '<p>Lütfen admin panelinden slider öğesi ekleyin.</p>';
    endif;
    ?>
</div>

<main>
    <div class="container">
        <!-- Diğer ana sayfa içerikleri buraya gelebilir -->
        <h1>Ana Sayfa</h1>
        <p>Bu alan ana sayfa içeriğidir.</p>
    </div>
</main>

<?php get_footer(); ?>
